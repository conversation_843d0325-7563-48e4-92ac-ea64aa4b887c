import { defineConfig, devices } from '@playwright/test';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Define storage state path to store authentication information
export const STORAGE_STATE = path.join(__dirname, 'playwright/.auth/user.json');

export default defineConfig({
  testDir: './tests',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  
  // Setup project for authentication
  projects: [
    {
      name: 'setup',
      testMatch: /auth\.setup\.ts/,
    },
    
    // Desktop Browser Projects - Login Tests
    {
      name: 'login-chromium',
      testMatch: /login.*\.test\.ts/,
      use: {
        ...devices['Desktop Chrome'],
        // No storage state - start unauthenticated for login tests
      },
    },
    // {
    //   name: 'login-firefox',
    //   testMatch: /login.*\.test\.ts/,
    //   use: {
    //     ...devices['Desktop Firefox'],
    //     // No storage state - start unauthenticated for login tests
    //   },
    // },
    // {
    //   name: 'login-webkit',
    //   testMatch: /login.*\.test\.ts/,
    //   use: {
    //     ...devices['Desktop Safari'],
    //     // No storage state - start unauthenticated for login tests
    //   },
    // },
    // {
    //   name: 'login-edge',
    //   testMatch: /login.*\.test\.ts/,
    //   use: {
    //     ...devices['Desktop Edge'],
    //     // No storage state - start unauthenticated for login tests
    //   },
    // },
    
    // Desktop Browser Projects - Browser Compatibility Tests
    {
      name: 'compatibility-chromium',
      testMatch: /browser-compatibility.*\.test\.ts/,
      use: {
        ...devices['Desktop Chrome'],
      },
    },
    // {
    //   name: 'compatibility-firefox',
    //   testMatch: /browser-compatibility.*\.test\.ts/,
    //   use: {
    //     ...devices['Desktop Firefox'],
    //   },
    // },
    // {
    //   name: 'compatibility-webkit',
    //   testMatch: /browser-compatibility.*\.test\.ts/,
    //   use: {
    //     ...devices['Desktop Safari'],
    //   },
    // },
    // {
    //   name: 'compatibility-edge',
    //   testMatch: /browser-compatibility.*\.test\.ts/,
    //   use: {
    //     ...devices['Desktop Edge'],
    //   },
    // },
    
    // Authenticated Tests - Multi-browser
    {
      name: 'auth-chromium',
      testMatch: /auth-flow\.test\.ts/,
      dependencies: ['setup'],
      use: {
        ...devices['Desktop Chrome'],
        storageState: STORAGE_STATE,
      },
    },
    // {
    //   name: 'auth-firefox',
    //   testMatch: /auth-flow\.test\.ts/,
    //   dependencies: ['setup'],
    //   use: {
    //     ...devices['Desktop Firefox'],
    //     storageState: STORAGE_STATE,
    //   },
    // },
    // {
    //   name: 'auth-webkit',
    //   testMatch: /auth-flow\.test\.ts/,
    //   dependencies: ['setup'],
    //   use: {
    //     ...devices['Desktop Safari'],
    //     storageState: STORAGE_STATE,
    //   },
    // },
  ],
  
  use: {
    baseURL: 'http://localhost:8000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
  },
  
  webServer: {
    command: 'npm run dev',
    port: 8000,
    reuseExistingServer: !process.env.CI,
  },
});
