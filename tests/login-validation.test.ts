import { test, expect } from '@playwright/test';

test.describe('Login Form Validation', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/login');
  });

  test.describe('Username validation', () => {
    test('should require username for form submission', async ({ page }) => {
      await page.fill('input[name="password"]', 'Testpass@1');
      
      // Form should be disabled when username is empty
      const submitButton = page.locator('button[type="submit"]');
      await expect(submitButton).toBeDisabled();
    });

    test('should filter invalid characters in username', async ({ page }) => {
      const usernameInput = page.locator('input[name="username"]');
      
      // Test various invalid characters - should be filtered out
      await usernameInput.fill('test@user');
      await expect(usernameInput).toHaveValue('testuser');
      
      await usernameInput.fill('test#user');
      await expect(usernameInput).toHaveValue('testuser');
      
      await usernameInput.fill('test user');
      await expect(usernameInput).toHaveValue('testuser');
      
      await usernameInput.fill('test$user');
      await expect(usernameInput).toHaveValue('testuser');
      
      await usernameInput.fill('test%user');
      await expect(usernameInput).toHaveValue('testuser');
      
      await usernameInput.fill('test&user');
      await expect(usernameInput).toHaveValue('testuser');
      
      await usernameInput.fill('test*user');
      await expect(usernameInput).toHaveValue('testuser');
    });

    test('should allow valid characters in username', async ({ page }) => {
      const usernameInput = page.locator('input[name="username"]');
      
      // Test valid characters: alphanumeric, dots, underscores, hyphens
      await usernameInput.fill('test.user_name-123');
      await expect(usernameInput).toHaveValue('test.user_name-123');
      
      await usernameInput.fill('user123');
      await expect(usernameInput).toHaveValue('user123');
      
      await usernameInput.fill('test_user');
      await expect(usernameInput).toHaveValue('test_user');
      
      await usernameInput.fill('test-user');
      await expect(usernameInput).toHaveValue('test-user');
      
      await usernameInput.fill('test.user');
      await expect(usernameInput).toHaveValue('test.user');
    });

    test('should handle mixed valid and invalid characters with 20 character limit', async ({ page }) => {
      const usernameInput = page.locator('input[name="username"]');
      
      // Mix of valid and invalid characters, should filter and limit to 20 chars
      await usernameInput.fill('test@user.name_123-abc!');
      await expect(usernameInput).toHaveValue('testuser.name_123-abc');
      
      // Test longer string that exceeds 20 characters
      await usernameInput.fill('verylongusername.with_hyphens-and.dots123456789');
      await expect(usernameInput).toHaveValue('verylongusername.wit');
    });

    test('should enforce 3-character minimum length requirement', async ({ page }) => {
      const usernameInput = page.locator('input[name="username"]');
      const passwordInput = page.locator('input[name="password"]');
      const submitButton = page.locator('button[type="submit"]');
      
      // Fill password to isolate username validation
      await passwordInput.fill('testpassword');
      
      // Test usernames shorter than 3 characters - button should be disabled
      await usernameInput.fill('ab');
      await expect(submitButton).toBeDisabled();
      
      await usernameInput.fill('a');
      await expect(submitButton).toBeDisabled();
      
      await usernameInput.fill('');
      await expect(submitButton).toBeDisabled();
      
      // Test valid 3-character username - button should be enabled
      await usernameInput.fill('abc');
      await expect(submitButton).toBeEnabled();
      
      // Test longer valid username - button should be enabled
      await usernameInput.fill('abcd');
      await expect(submitButton).toBeEnabled();
    });
  });

  test.describe('Password validation', () => {
    test('should require password', async ({ page }) => {
      await page.fill('input[name="username"]', 'testuser1');
      await page.click('button[type="submit"]');
      
      await expect(page.locator('text=Password is required')).toBeVisible();
    });

    test('should accept any password characters', async ({ page }) => {
      const passwordInput = page.locator('input[name="password"]');
      
      // Test special characters in password
      await passwordInput.fill('password@123!#$%');
      await expect(passwordInput).toHaveValue('password@123!#$%');
    });

    test('should mask password input', async ({ page }) => {
      const passwordInput = page.locator('input[name="password"]');
      
      await expect(passwordInput).toHaveAttribute('type', 'password');
    });
  });

  test.describe('Form submission validation', () => {
    test('should prevent submission with empty form', async ({ page }) => {
      await page.click('button[type="submit"]');
      
      // Should show validation errors for both fields
      await expect(page.locator('text=Username is required')).toBeVisible();
      await expect(page.locator('text=Password is required')).toBeVisible();
      
      // Should remain on login page
      await expect(page).toHaveURL('/login');
    });

    test('should prevent submission with only username', async ({ page }) => {
      await page.fill('input[name="username"]', 'testuser1');
      await page.click('button[type="submit"]');
      
      // Should show password validation error
      await expect(page.locator('text=Password is required')).toBeVisible();
      
      // Should remain on login page
      await expect(page).toHaveURL('/login');
    });

    test('should prevent submission with only password', async ({ page }) => {
      await page.fill('input[name="password"]', 'Testpass@1');
      await page.click('button[type="submit"]');
      
      // Should show username validation error
      await expect(page.locator('text=Username is required')).toBeVisible();
      
      // Should remain on login page
      await expect(page).toHaveURL('/login');
    });

    test('should allow submission with valid data', async ({ page }) => {
      await page.fill('input[name="username"]', 'testuser1');
      await page.fill('input[name="password"]', 'Testpass@1');
      await page.click('button[type="submit"]');
      
      // Should redirect on successful submission
      await expect(page).toHaveURL('/chat_center');
    });
  });

  test.describe('Real-time validation', () => {
    test('should validate username on blur', async ({ page }) => {
      const usernameInput = page.locator('input[name="username"]');
      
      // Focus and blur empty field
      await usernameInput.focus();
      await usernameInput.blur();
      
      // Should show validation error
      await expect(page.locator('text=Username is required')).toBeVisible();
    });

    test('should clear validation error when field is filled', async ({ page }) => {
      const usernameInput = page.locator('input[name="username"]');
      
      // Trigger validation error
      await usernameInput.focus();
      await usernameInput.blur();
      await expect(page.locator('text=Username is required')).toBeVisible();
      
      // Fill the field
      await usernameInput.fill('testuser1');
      
      // Validation error should disappear
      await expect(page.locator('text=Username is required')).not.toBeVisible();
    });
  });

  test.describe('Edge cases', () => {
    test('should handle rapid character input', async ({ page }) => {
      const usernameInput = page.locator('input[name="username"]');
      
      // Rapid typing with mixed characters
      await usernameInput.fill('t@e#s$t%u^s&e*r');
      
      // Should filter out invalid characters
      await expect(usernameInput).toHaveValue('testuser');
    });

    test('should handle copy-paste with invalid characters', async ({ page }) => {
      const usernameInput = page.locator('input[name="username"]');
      
      // Simulate paste with invalid characters
      await usernameInput.focus();
      
      // Use page.evaluate to simulate clipboard paste event
      await page.evaluate(() => {
        const input = document.querySelector('input[name="username"]') as HTMLInputElement;
        const pasteEvent = new ClipboardEvent('paste', {
          clipboardData: new DataTransfer()
        });
        pasteEvent.clipboardData?.setData('text', 'test@user#name$123');
        input.dispatchEvent(pasteEvent);
      });
      
      // Should filter out invalid characters
      await expect(usernameInput).toHaveValue('testusername123');
    });

    test('should handle up to 20 characters in usernames', async ({ page }) => {
      const usernameInput = page.locator('input[name="username"]');
      
      // Test with very long username
      const longUsername = 'a'.repeat(20);
      await usernameInput.fill(longUsername);
      
      // Should accept the long username (if no maxlength restriction)
      await expect(usernameInput).toHaveValue(longUsername);
    });

    test('should not allow special characters in password', async ({ page }) => {
      const passwordInput = page.locator('input[name="password"]');
      
      // Test with special characters
      await passwordInput.fill('pass@word#123');
      
      // Should accept the password with special characters
      await expect(passwordInput).toHaveValue('pass@word#123');
    });
  });
});